// Intégration du système de carte SIM dans l'interface du téléphone

// Fonction pour ajouter le bouton de gestion SIM dans les paramètres
function addSimSettingsButton() {
    // Vérifier si le bouton existe déjà
    if (document.getElementById('sim-settings-btn')) {
        return;
    }

    // Trouver le conteneur des paramètres
    const settingsContainer = document.querySelector('.settings-menu') || 
                             document.querySelector('.phone-settings') ||
                             document.querySelector('[data-app="settings"]');
    
    if (!settingsContainer) {
        console.log('Conteneur des paramètres non trouvé');
        return;
    }

    // Créer le bouton de gestion SIM
    const simButton = document.createElement('div');
    simButton.id = 'sim-settings-btn';
    simButton.className = 'setting-item sim-setting-item';
    simButton.innerHTML = `
        <div class="setting-icon">
            <i class="fas fa-sim-card"></i>
        </div>
        <div class="setting-content">
            <div class="setting-title">Carte SIM</div>
            <div class="setting-description">Gérer votre carte SIM</div>
        </div>
        <div class="setting-arrow">
            <i class="fas fa-chevron-right"></i>
        </div>
    `;

    // Ajouter l'événement de clic
    simButton.addEventListener('click', function() {
        openSimSettings();
    });

    // Ajouter le bouton au conteneur
    settingsContainer.appendChild(simButton);
}

// Fonction pour ouvrir les paramètres SIM
function openSimSettings() {
    const phoneNumber = getCurrentPhoneNumber();
    if (!phoneNumber) {
        showNotification('Numéro de téléphone non trouvé', 'error');
        return;
    }

    // Déclencher l'événement côté client
    fetch(`https://${GetParentResourceName()}/openSimSettings`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            phoneNumber: phoneNumber
        })
    });
}

// Fonction pour obtenir le numéro de téléphone actuel
function getCurrentPhoneNumber() {
    // Adapter selon votre système de téléphone
    if (window.phoneData && window.phoneData.phoneNumber) {
        return window.phoneData.phoneNumber;
    }
    
    if (window.phone && window.phone.number) {
        return window.phone.number;
    }
    
    // Essayer de récupérer depuis les métadonnées
    const phoneElement = document.querySelector('[data-phone-number]');
    if (phoneElement) {
        return phoneElement.getAttribute('data-phone-number');
    }
    
    return null;
}

// Fonction pour afficher une notification
function showNotification(message, type = 'info') {
    // Adapter selon votre système de notification
    if (window.showNotification) {
        window.showNotification(message, type);
    } else if (window.notify) {
        window.notify(message, type);
    } else {
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
}

// Fonction pour afficher les informations de la carte SIM
function displaySimInfo(simInfo) {
    const simInfoContainer = document.getElementById('sim-info-container');
    if (!simInfoContainer) {
        return;
    }

    if (simInfo && simInfo.name) {
        simInfoContainer.innerHTML = `
            <div class="sim-info-card">
                <div class="sim-info-header">
                    <i class="fas fa-sim-card"></i>
                    <span>Carte SIM installée</span>
                </div>
                <div class="sim-info-details">
                    <div class="sim-detail">
                        <span class="label">Type:</span>
                        <span class="value">${simInfo.type || 'Inconnu'}</span>
                    </div>
                    <div class="sim-detail">
                        <span class="label">Numéro:</span>
                        <span class="value">${simInfo.phone_number || 'N/A'}</span>
                    </div>
                    <div class="sim-detail">
                        <span class="label">Installée le:</span>
                        <span class="value">${formatDate(simInfo.installed_date)}</span>
                    </div>
                </div>
                <div class="sim-benefits">
                    <h4>Avantages:</h4>
                    <ul>
                        ${simInfo.benefits.call_quality ? `<li>Qualité d'appel: ${(simInfo.benefits.call_quality * 100)}%</li>` : ''}
                        ${simInfo.benefits.data_speed ? `<li>Vitesse de données: ${(simInfo.benefits.data_speed * 100)}%</li>` : ''}
                        ${simInfo.benefits.signal_strength ? `<li>Force du signal: ${(simInfo.benefits.signal_strength * 100)}%</li>` : ''}
                        ${simInfo.benefits.unlimited_data ? '<li>Données illimitées</li>' : ''}
                    </ul>
                </div>
            </div>
        `;
    } else {
        simInfoContainer.innerHTML = `
            <div class="sim-info-card no-sim">
                <div class="sim-info-header">
                    <i class="fas fa-sim-card"></i>
                    <span>Aucune carte SIM</span>
                </div>
                <div class="sim-info-message">
                    Aucune carte SIM n'est installée dans ce téléphone.
                </div>
            </div>
        `;
    }
}

// Fonction pour formater une date
function formatDate(timestamp) {
    if (!timestamp) return 'N/A';
    
    const date = new Date(timestamp * 1000);
    return date.toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// Styles CSS pour l'interface SIM
function addSimStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .sim-setting-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .sim-setting-item:hover {
            background-color: #f5f5f5;
        }

        .setting-icon {
            margin-right: 15px;
            font-size: 20px;
            color: #007AFF;
        }

        .setting-content {
            flex: 1;
        }

        .setting-title {
            font-weight: 600;
            margin-bottom: 2px;
        }

        .setting-description {
            font-size: 12px;
            color: #666;
        }

        .setting-arrow {
            color: #ccc;
        }

        .sim-info-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .sim-info-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .sim-info-header i {
            margin-right: 10px;
            color: #007AFF;
        }

        .sim-info-details {
            margin-bottom: 15px;
        }

        .sim-detail {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .sim-detail .label {
            font-weight: 500;
            color: #666;
        }

        .sim-benefits h4 {
            margin-bottom: 10px;
            color: #333;
        }

        .sim-benefits ul {
            list-style: none;
            padding: 0;
        }

        .sim-benefits li {
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .no-sim {
            text-align: center;
            color: #666;
        }

        .sim-info-message {
            margin-top: 15px;
            font-style: italic;
        }
    `;
    document.head.appendChild(style);
}

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
    addSimStyles();
    
    // Ajouter le bouton SIM après un délai pour s'assurer que l'interface est chargée
    setTimeout(addSimSettingsButton, 1000);
    
    // Observer les changements dans le DOM pour réajouter le bouton si nécessaire
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                addSimSettingsButton();
            }
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});

// Export des fonctions pour utilisation externe
window.SimIntegration = {
    addSimSettingsButton,
    openSimSettings,
    displaySimInfo,
    getCurrentPhoneNumber
};
