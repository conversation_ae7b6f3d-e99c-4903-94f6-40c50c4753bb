{"name": "terser", "description": "JavaScript parser, mangler/compressor and beautifier toolkit for ES6+", "homepage": "https://terser.org", "author": "<PERSON><PERSON> <<EMAIL>> (http://lisperator.net/)", "license": "BSD-2-<PERSON><PERSON>", "version": "4.8.0", "engines": {"node": ">=6.0.0"}, "maintainers": ["<PERSON><PERSON><PERSON> <<EMAIL>>"], "repository": "https://github.com/terser/terser", "main": "dist/bundle.min.js", "types": "tools/terser.d.ts", "bin": {"terser": "bin/terser"}, "files": ["bin", "dist", "tools", "LICENSE", "README.md", "CHANGELOG.md", "PATRONS.md"], "dependencies": {"commander": "^2.20.0", "source-map": "~0.6.1", "source-map-support": "~0.5.12"}, "devDependencies": {"acorn": "^7.1.1", "astring": "^1.4.1", "eslint": "^6.3.0", "eslump": "^2.0.0", "mocha": "^7.1.2", "mochallel": "^2.0.0", "pre-commit": "^1.2.2", "rimraf": "^3.0.0", "rollup": "2.0.6", "rollup-plugin-terser": "5.3.0", "semver": "^7.1.3"}, "scripts": {"test": "npm run build -- --configTest && node test/run-tests.js", "test:compress": "npm run build -- --configTest && node test/compress.js", "test:mocha": "npm run build -- --configTest && node test/mocha.js", "lint": "eslint lib", "lint-fix": "eslint --fix lib", "build": "rimraf dist/* && rollup --config --silent", "prepare": "npm run build", "postversion": "echo 'Remember to update the changelog!'"}, "keywords": ["uglify", "terser", "uglify-es", "uglify-js", "minify", "minifier", "javascript", "ecmascript", "es5", "es6", "es7", "es8", "es2015", "es2016", "es2017", "async", "await"], "eslintConfig": {"parserOptions": {"sourceType": "module"}, "env": {"es6": true}, "globals": {"describe": false, "it": false, "require": false, "global": false, "process": false}, "rules": {"brace-style": ["error", "1tbs", {"allowSingleLine": true}], "quotes": ["error", "double", "avoid-escape"], "no-debugger": "error", "no-undef": "error", "no-unused-vars": ["error", {"varsIgnorePattern": "^_$"}], "no-tabs": "error", "semi": ["error", "always"], "no-extra-semi": "error", "no-irregular-whitespace": "error", "space-before-blocks": ["error", "always"]}}, "pre-commit": ["lint-fix", "test"]}