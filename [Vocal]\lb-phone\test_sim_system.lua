-- Script de test pour le système de carte SIM
-- À exécuter côté serveur pour tester les fonctionnalités

local TestSim = {}

-- Fonction pour tester l'ajout d'items SIM
function TestSim.TestAddSimItems()
    print("^3[Test SIM] ^7Test d'ajout des items cartes SIM...")
    
    local simTypes = {"sim_card_basic", "sim_card_premium", "sim_card_unlimited"}
    
    for _, simType in ipairs(simTypes) do
        local itemData = ScriptShared.Items:Get(simType)
        if itemData then
            print("^2[Test SIM] ^7✓ Item " .. simType .. " trouvé: " .. itemData.label)
        else
            print("^1[Test SIM] ^7✗ Item " .. simType .. " non trouvé!")
        end
    end
end

-- Fonction pour tester l'installation d'une carte SIM
function TestSim.TestInstallSim(source, phoneNumber, simType)
    print("^3[Test SIM] ^7Test d'installation de carte SIM...")
    
    if not source or not phoneNumber or not simType then
        print("^1[Test SIM] ^7✗ Paramètres manquants pour le test")
        return
    end
    
    -- Ajouter une carte SIM à l'inventaire du joueur pour le test
    if GetResourceState("ox_inventory") == "started" then
        exports.ox_inventory:AddItem(source, simType, 1)
    elseif GetResourceState("qb-inventory") == "started" then
        local qPlayer = QB.Functions.GetPlayer(source)
        if qPlayer then
            qPlayer.Functions.AddItem(simType, 1)
        end
    end
    
    -- Tester l'installation
    local success, message = exports['lb-phone']:InstallSimCard(source, phoneNumber, simType)
    
    if success then
        print("^2[Test SIM] ^7✓ Installation réussie: " .. message)
    else
        print("^1[Test SIM] ^7✗ Installation échouée: " .. message)
    end
    
    return success
end

-- Fonction pour tester la récupération des informations SIM
function TestSim.TestGetSimInfo(source, phoneNumber)
    print("^3[Test SIM] ^7Test de récupération des informations SIM...")
    
    local simInfo = exports['lb-phone']:GetInstalledSimCard(source, phoneNumber)
    
    if simInfo then
        print("^2[Test SIM] ^7✓ Informations SIM récupérées:")
        print("  - Type: " .. (simInfo.type or "N/A"))
        print("  - Numéro: " .. (simInfo.phone_number or "N/A"))
        print("  - Date d'installation: " .. (simInfo.installed_date or "N/A"))
    else
        print("^1[Test SIM] ^7✗ Aucune information SIM trouvée")
    end
    
    return simInfo
end

-- Fonction pour tester la suppression d'une carte SIM
function TestSim.TestRemoveSim(source, phoneNumber)
    print("^3[Test SIM] ^7Test de suppression de carte SIM...")
    
    local success, message = exports['lb-phone']:RemoveSimCard(source, phoneNumber)
    
    if success then
        print("^2[Test SIM] ^7✓ Suppression réussie: " .. message)
    else
        print("^1[Test SIM] ^7✗ Suppression échouée: " .. message)
    end
    
    return success
end

-- Fonction pour tester le système complet
function TestSim.RunFullTest(source, phoneNumber)
    print("^3[Test SIM] ^7=== DÉBUT DU TEST COMPLET ===")
    
    -- Test 1: Vérifier les items
    TestSim.TestAddSimItems()
    
    -- Test 2: Installer une carte SIM basique
    local installSuccess = TestSim.TestInstallSim(source, phoneNumber, "sim_card_basic")
    
    if installSuccess then
        -- Test 3: Récupérer les informations
        local simInfo = TestSim.TestGetSimInfo(source, phoneNumber)
        
        -- Test 4: Supprimer la carte SIM
        local removeSuccess = TestSim.TestRemoveSim(source, phoneNumber)
        
        if removeSuccess then
            print("^2[Test SIM] ^7=== TEST COMPLET RÉUSSI ===")
        else
            print("^1[Test SIM] ^7=== ÉCHEC DU TEST (suppression) ===")
        end
    else
        print("^1[Test SIM] ^7=== ÉCHEC DU TEST (installation) ===")
    end
end

-- Commandes de test
RegisterCommand("testsim", function(source, args)
    if source == 0 then
        print("^1[Test SIM] ^7Cette commande doit être exécutée par un joueur")
        return
    end
    
    local phoneNumber = args[1]
    if not phoneNumber then
        TriggerClientEvent('chat:addMessage', source, {
            color = {255, 0, 0},
            multiline = true,
            args = {"Test SIM", "Usage: /testsim [numéro_téléphone]"}
        })
        return
    end
    
    TestSim.RunFullTest(source, phoneNumber)
end, false)

RegisterCommand("testsimitems", function(source, args)
    TestSim.TestAddSimItems()
end, false)

RegisterCommand("addsim", function(source, args)
    if source == 0 then
        print("^1[Test SIM] ^7Cette commande doit être exécutée par un joueur")
        return
    end
    
    local simType = args[1] or "sim_card_basic"
    local quantity = tonumber(args[2]) or 1
    
    if GetResourceState("ox_inventory") == "started" then
        exports.ox_inventory:AddItem(source, simType, quantity)
    elseif GetResourceState("qb-inventory") == "started" then
        local qPlayer = QB.Functions.GetPlayer(source)
        if qPlayer then
            qPlayer.Functions.AddItem(simType, quantity)
        end
    end
    
    TriggerClientEvent('chat:addMessage', source, {
        color = {0, 255, 0},
        multiline = true,
        args = {"Test SIM", "Ajouté " .. quantity .. "x " .. simType}
    })
end, false)

-- Fonction pour tester la compatibilité avec différents systèmes d'inventaire
function TestSim.TestInventoryCompatibility()
    print("^3[Test SIM] ^7Test de compatibilité des inventaires...")
    
    if GetResourceState("ox_inventory") == "started" then
        print("^2[Test SIM] ^7✓ ox_inventory détecté")
    elseif GetResourceState("qb-inventory") == "started" then
        print("^2[Test SIM] ^7✓ qb-inventory détecté")
    else
        print("^1[Test SIM] ^7✗ Aucun inventaire supporté détecté")
    end
    
    -- Tester les frameworks
    if GetResourceState("es_extended") == "started" then
        print("^2[Test SIM] ^7✓ ESX détecté")
    elseif GetResourceState("qb-core") == "started" then
        print("^2[Test SIM] ^7✓ QBCore détecté")
    elseif GetResourceState("qbox-core") == "started" then
        print("^2[Test SIM] ^7✓ QBox détecté")
    else
        print("^1[Test SIM] ^7✗ Aucun framework supporté détecté")
    end
end

-- Initialisation des tests
CreateThread(function()
    Wait(5000) -- Attendre que tous les systèmes soient chargés
    
    print("^3[Test SIM] ^7Système de test SIM initialisé")
    print("^3[Test SIM] ^7Commandes disponibles:")
    print("  - /testsim [numéro_téléphone] : Test complet")
    print("  - /testsimitems : Test des items")
    print("  - /addsim [type] [quantité] : Ajouter une carte SIM")
    
    TestSim.TestInventoryCompatibility()
end)

-- Export pour utilisation externe
exports("RunSimTest", TestSim.RunFullTest)
exports("TestSimItems", TestSim.TestAddSimItems)
