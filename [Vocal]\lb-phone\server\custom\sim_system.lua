-- Système de gestion des cartes SIM pour les téléphones
-- Inspiré du système d'accessoires d'armes

-- Charger la configuration
if not Config.SimCard then
    print("^1[LB Phone SIM] ^7Configuration SIM non trouvée, chargement du fichier de config...")
    local configFile = LoadResourceFile(GetCurrentResourceName(), "config/sim_config.lua")
    if configFile then
        load(configFile)()
    else
        print("^1[LB Phone SIM] ^7Erreur: Impossible de charger sim_config.lua")
        return
    end
end

local SimSystem = {}

-- Fonction pour obtenir les métadonnées d'un téléphone
function SimSystem.GetPhoneMetadata(source, phoneNumber)
    if not source or not phoneNumber then
        return nil
    end
    
    -- Selon votre système d'inventaire, adapter cette fonction
    if Config.Item.Unique then
        if GetResourceState("ox_inventory") == "started" then
            local phones = exports.ox_inventory:Search(source, "slots", Config.Item.Name)
            if phones then
                for i = 1, #phones do
                    local phone = phones[i]
                    if phone?.metadata?.lbPhoneNumber == phoneNumber then
                        return phone.metadata
                    end
                end
            end
        elseif GetResourceState("qb-inventory") == "started" then
            local qPlayer = QB.Functions.GetPlayer(source)
            if qPlayer then
                local items = qPlayer.PlayerData.items
                for i = 1, #items do
                    local item = items[i]
                    if item and item.name == Config.Item.Name and item.info.lbPhoneNumber == phoneNumber then
                        return item.info
                    end
                end
            end
        end
    end
    
    return nil
end

-- Fonction pour mettre à jour les métadonnées d'un téléphone
function SimSystem.UpdatePhoneMetadata(source, phoneNumber, metadata)
    if not source or not phoneNumber or not metadata then
        return false
    end
    
    if Config.Item.Unique then
        if GetResourceState("ox_inventory") == "started" then
            local phones = exports.ox_inventory:Search(source, "slots", Config.Item.Name)
            if phones then
                for i = 1, #phones do
                    local phone = phones[i]
                    if phone?.metadata?.lbPhoneNumber == phoneNumber then
                        exports.ox_inventory:SetMetadata(source, phone.slot, metadata)
                        return true
                    end
                end
            end
        elseif GetResourceState("qb-inventory") == "started" then
            local qPlayer = QB.Functions.GetPlayer(source)
            if qPlayer then
                local items = qPlayer.PlayerData.items
                for i = 1, #items do
                    local item = items[i]
                    if item and item.name == Config.Item.Name and item.info.lbPhoneNumber == phoneNumber then
                        item.info = metadata
                        qPlayer.Functions.SetInventory(items, true)
                        return true
                    end
                end
            end
        end
    end
    
    return false
end

-- Fonction pour installer une carte SIM dans un téléphone
function SimSystem.InstallSimCard(source, phoneNumber, simCardName)
    if not Config.SimCard.Enabled then
        return false, "Système de carte SIM désactivé"
    end
    
    if not Config.SimCard.IsValidSimCard(simCardName) then
        return false, Config.SimCard.Messages.invalid_sim_card
    end
    
    local phoneMetadata = SimSystem.GetPhoneMetadata(source, phoneNumber)
    if not phoneMetadata then
        return false, Config.SimCard.Messages.phone_not_found
    end
    
    -- Initialiser les métadonnées SIM si elles n'existent pas
    if not phoneMetadata.sim_slot then
        phoneMetadata.sim_slot = Config.SimCard.GetDefaultPhoneMetadata().sim_slot
    end
    
    -- Vérifier si une carte SIM est déjà installée
    if phoneMetadata.sim_slot.name then
        return false, Config.SimCard.Messages.sim_already_installed
    end
    
    -- Vérifier si le joueur a la carte SIM
    local hasSimCard = false
    if GetResourceState("ox_inventory") == "started" then
        hasSimCard = (exports.ox_inventory:Search(source, "count", simCardName) or 0) > 0
    elseif GetResourceState("qb-inventory") == "started" then
        local qPlayer = QB.Functions.GetPlayer(source)
        if qPlayer then
            hasSimCard = (qPlayer.Functions.GetItemByName(simCardName)?.amount or 0) > 0
        end
    end
    
    if not hasSimCard then
        return false, "Vous n'avez pas cette carte SIM"
    end
    
    -- Retirer la carte SIM de l'inventaire
    if GetResourceState("ox_inventory") == "started" then
        exports.ox_inventory:RemoveItem(source, simCardName, 1)
    elseif GetResourceState("qb-inventory") == "started" then
        local qPlayer = QB.Functions.GetPlayer(source)
        if qPlayer then
            qPlayer.Functions.RemoveItem(simCardName, 1)
        end
    end
    
    -- Installer la carte SIM
    local simType = Config.SimCard.GetSimCardType(simCardName)
    phoneMetadata.sim_slot = {
        name = simCardName,
        type = simType.name,
        phone_number = phoneNumber,
        benefits = simType.benefits,
        installed_date = os.time()
    }
    
    -- Mettre à jour les métadonnées du téléphone
    local success = SimSystem.UpdatePhoneMetadata(source, phoneNumber, phoneMetadata)
    if success then
        return true, Config.SimCard.Messages.sim_installed
    else
        -- Rendre la carte SIM si la mise à jour échoue
        if GetResourceState("ox_inventory") == "started" then
            exports.ox_inventory:AddItem(source, simCardName, 1)
        elseif GetResourceState("qb-inventory") == "started" then
            local qPlayer = QB.Functions.GetPlayer(source)
            if qPlayer then
                qPlayer.Functions.AddItem(simCardName, 1)
            end
        end
        return false, "Erreur lors de l'installation de la carte SIM"
    end
end

-- Fonction pour retirer une carte SIM d'un téléphone
function SimSystem.RemoveSimCard(source, phoneNumber)
    if not Config.SimCard.Enabled then
        return false, "Système de carte SIM désactivé"
    end
    
    local phoneMetadata = SimSystem.GetPhoneMetadata(source, phoneNumber)
    if not phoneMetadata then
        return false, Config.SimCard.Messages.phone_not_found
    end
    
    if not phoneMetadata.sim_slot or not phoneMetadata.sim_slot.name then
        return false, Config.SimCard.Messages.no_sim_installed
    end
    
    local simCardName = phoneMetadata.sim_slot.name
    
    -- Remettre la carte SIM dans l'inventaire
    if GetResourceState("ox_inventory") == "started" then
        exports.ox_inventory:AddItem(source, simCardName, 1)
    elseif GetResourceState("qb-inventory") == "started" then
        local qPlayer = QB.Functions.GetPlayer(source)
        if qPlayer then
            qPlayer.Functions.AddItem(simCardName, 1)
        end
    end
    
    -- Retirer la carte SIM des métadonnées
    phoneMetadata.sim_slot = Config.SimCard.GetDefaultPhoneMetadata().sim_slot
    
    -- Mettre à jour les métadonnées du téléphone
    local success = SimSystem.UpdatePhoneMetadata(source, phoneNumber, phoneMetadata)
    if success then
        return true, Config.SimCard.Messages.sim_removed
    else
        return false, "Erreur lors du retrait de la carte SIM"
    end
end

-- Fonction pour obtenir les informations de la carte SIM installée
function SimSystem.GetInstalledSimCard(source, phoneNumber)
    local phoneMetadata = SimSystem.GetPhoneMetadata(source, phoneNumber)
    if not phoneMetadata or not phoneMetadata.sim_slot or not phoneMetadata.sim_slot.name then
        return nil
    end
    
    return phoneMetadata.sim_slot
end

-- Exports pour utiliser le système
exports("InstallSimCard", SimSystem.InstallSimCard)
exports("RemoveSimCard", SimSystem.RemoveSimCard)
exports("GetInstalledSimCard", SimSystem.GetInstalledSimCard)

-- Events pour les commandes
RegisterNetEvent("lb-phone:installSimCard", function(phoneNumber, simCardName)
    local source = source
    local success, message = SimSystem.InstallSimCard(source, phoneNumber, simCardName)
    TriggerClientEvent("lb-phone:simCardResult", source, success, message)
end)

RegisterNetEvent("lb-phone:removeSimCard", function(phoneNumber)
    local source = source
    local success, message = SimSystem.RemoveSimCard(source, phoneNumber)
    TriggerClientEvent("lb-phone:simCardResult", source, success, message)
end)

-- Event pour utiliser une carte SIM depuis l'inventaire
RegisterNetEvent("lb-phone:useSimCard", function(data)
    local source = source
    local simType = data.type

    if not simType then
        TriggerClientEvent("lb-phone:simCardResult", source, false, "Type de carte SIM invalide")
        return
    end

    -- Demander au joueur de choisir un téléphone
    TriggerClientEvent("lb-phone:selectPhoneForSim", source, simType)
end)

-- Event pour installer une carte SIM sur un téléphone sélectionné
RegisterNetEvent("lb-phone:installSimOnPhone", function(phoneNumber, simType)
    local source = source
    local success, message = SimSystem.InstallSimCard(source, phoneNumber, simType)
    TriggerClientEvent("lb-phone:simCardResult", source, success, message)
end)

-- Callbacks pour le client
ESX.RegisterServerCallback("lb-phone:getInstalledSimCard", function(source, cb, phoneNumber)
    local simInfo = SimSystem.GetInstalledSimCard(source, phoneNumber)
    cb(simInfo)
end)

ESX.RegisterServerCallback("lb-phone:getAvailableSimCards", function(source, cb)
    local availableSimCards = {}

    for _, simType in ipairs(Config.SimCard.Types) do
        local count = 0

        if GetResourceState("ox_inventory") == "started" then
            count = exports.ox_inventory:Search(source, "count", simType.name) or 0
        elseif GetResourceState("qb-inventory") == "started" then
            local qPlayer = QB.Functions.GetPlayer(source)
            if qPlayer then
                count = qPlayer.Functions.GetItemByName(simType.name)?.amount or 0
            end
        end

        if count > 0 then
            table.insert(availableSimCards, {
                name = simType.name,
                label = simType.label,
                description = simType.description,
                count = count
            })
        end
    end

    cb(availableSimCards)
end)

-- Callback pour obtenir les téléphones du joueur
ESX.RegisterServerCallback("lb-phone:getPlayerPhones", function(source, cb)
    local phones = {}

    if Config.Item.Unique then
        if GetResourceState("ox_inventory") == "started" then
            local playerPhones = exports.ox_inventory:Search(source, "slots", Config.Item.Name)
            if playerPhones then
                for i = 1, #playerPhones do
                    local phone = playerPhones[i]
                    if phone?.metadata?.lbPhoneNumber then
                        table.insert(phones, {
                            phone_number = phone.metadata.lbPhoneNumber,
                            name = phone.metadata.lbPhoneName or "Téléphone"
                        })
                    end
                end
            end
        elseif GetResourceState("qb-inventory") == "started" then
            local qPlayer = QB.Functions.GetPlayer(source)
            if qPlayer then
                local items = qPlayer.PlayerData.items
                for i = 1, #items do
                    local item = items[i]
                    if item and item.name == Config.Item.Name and item.info.lbPhoneNumber then
                        table.insert(phones, {
                            phone_number = item.info.lbPhoneNumber,
                            name = item.info.lbPhoneName or "Téléphone"
                        })
                    end
                end
            end
        end
    else
        -- Si les téléphones ne sont pas uniques, obtenir depuis la base de données
        local identifier = GetIdentifier(source)
        if identifier then
            local result = MySQL.query.await("SELECT phone_number, name FROM phone_phones WHERE owner_id = ?", {identifier})
            if result then
                for i = 1, #result do
                    table.insert(phones, {
                        phone_number = result[i].phone_number,
                        name = result[i].name or "Téléphone"
                    })
                end
            end
        end
    end

    cb(phones)
end)

print("^2[LB Phone SIM] ^7Système de carte SIM chargé avec succès")
