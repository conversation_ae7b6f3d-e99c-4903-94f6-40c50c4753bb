-- Système de gestion des cartes SIM côté client

local SimSystemClient = {}

-- Fonction pour afficher une notification
function SimSystemClient.ShowNotification(message, type)
    type = type or "info"
    
    -- Adapter selon votre système de notification
    if GetResourceState("ox_lib") == "started" then
        exports.ox_lib:notify({
            title = "Carte SIM",
            description = message,
            type = type
        })
    elseif GetResourceState("qb-core") == "started" then
        QBCore.Functions.Notify(message, type)
    elseif GetResourceState("esx_notify") == "started" then
        exports["esx_notify"]:Notify("info", 5000, message)
    else
        -- Notification par défaut
        SetNotificationTextEntry("STRING")
        AddTextComponentString(message)
        DrawNotification(false, false)
    end
end

-- Fonction pour ouvrir le menu de gestion des cartes SIM
function SimSystemClient.OpenSimMenu(phoneNumber)
    if not phoneNumber then
        SimSystemClient.ShowNotification("Numéro de téléphone requis", "error")
        return
    end
    
    -- Obtenir les informations de la carte SIM installée
    ESX.TriggerServerCallback("lb-phone:getInstalledSimCard", function(simInfo)
        local menuOptions = {}
        
        if simInfo then
            -- Carte SIM installée
            table.insert(menuOptions, {
                title = "Carte SIM installée",
                description = "Type: " .. (simInfo.type or "Inconnu"),
                icon = "sim-card",
                disabled = true
            })
            
            table.insert(menuOptions, {
                title = "Retirer la carte SIM",
                description = "Retirer la carte SIM actuelle",
                icon = "eject",
                onSelect = function()
                    TriggerServerEvent("lb-phone:removeSimCard", phoneNumber)
                end
            })
        else
            -- Aucune carte SIM installée
            table.insert(menuOptions, {
                title = "Aucune carte SIM",
                description = "Aucune carte SIM installée",
                icon = "sim-card",
                disabled = true
            })
        end
        
        -- Options pour installer une nouvelle carte SIM
        table.insert(menuOptions, {
            title = "Installer une carte SIM",
            description = "Choisir une carte SIM à installer",
            icon = "plus",
            onSelect = function()
                SimSystemClient.OpenSimInstallMenu(phoneNumber)
            end
        })
        
        -- Ouvrir le menu selon votre système
        if GetResourceState("ox_lib") == "started" then
            exports.ox_lib:registerContext({
                id = "sim_menu",
                title = "Gestion Carte SIM",
                options = menuOptions
            })
            exports.ox_lib:showContext("sim_menu")
        elseif GetResourceState("qb-menu") == "started" then
            exports["qb-menu"]:openMenu(menuOptions)
        else
            -- Menu par défaut ou adaptation nécessaire
            print("Système de menu non supporté")
        end
    end, phoneNumber)
end

-- Fonction pour ouvrir le menu d'installation de carte SIM
function SimSystemClient.OpenSimInstallMenu(phoneNumber)
    -- Obtenir la liste des cartes SIM disponibles dans l'inventaire
    ESX.TriggerServerCallback("lb-phone:getAvailableSimCards", function(simCards)
        if not simCards or #simCards == 0 then
            SimSystemClient.ShowNotification("Aucune carte SIM disponible", "error")
            return
        end
        
        local menuOptions = {}
        
        for _, simCard in ipairs(simCards) do
            table.insert(menuOptions, {
                title = simCard.label,
                description = simCard.description .. " (Quantité: " .. simCard.count .. ")",
                icon = "sim-card",
                onSelect = function()
                    TriggerServerEvent("lb-phone:installSimCard", phoneNumber, simCard.name)
                end
            })
        end
        
        -- Ouvrir le menu selon votre système
        if GetResourceState("ox_lib") == "started" then
            exports.ox_lib:registerContext({
                id = "sim_install_menu",
                title = "Installer Carte SIM",
                options = menuOptions
            })
            exports.ox_lib:showContext("sim_install_menu")
        elseif GetResourceState("qb-menu") == "started" then
            exports["qb-menu"]:openMenu(menuOptions)
        else
            print("Système de menu non supporté")
        end
    end)
end

-- Event pour recevoir les résultats des opérations SIM
RegisterNetEvent("lb-phone:simCardResult", function(success, message)
    if success then
        SimSystemClient.ShowNotification(message, "success")
    else
        SimSystemClient.ShowNotification(message, "error")
    end
end)

-- Commande pour ouvrir le menu SIM (pour test)
RegisterCommand("simcard", function(source, args)
    local phoneNumber = args[1]
    if not phoneNumber then
        SimSystemClient.ShowNotification("Usage: /simcard [numéro_téléphone]", "error")
        return
    end
    
    SimSystemClient.OpenSimMenu(phoneNumber)
end, false)

-- Export pour utiliser le système depuis d'autres scripts
exports("OpenSimMenu", SimSystemClient.OpenSimMenu)

-- Event pour sélectionner un téléphone lors de l'utilisation d'une carte SIM
RegisterNetEvent("lb-phone:selectPhoneForSim", function(simType)
    -- Obtenir la liste des téléphones du joueur
    ESX.TriggerServerCallback("lb-phone:getPlayerPhones", function(phones)
        if not phones or #phones == 0 then
            SimSystemClient.ShowNotification("Aucun téléphone trouvé", "error")
            return
        end

        local menuOptions = {}

        for _, phone in ipairs(phones) do
            table.insert(menuOptions, {
                title = phone.name or "Téléphone",
                description = "Numéro: " .. phone.phone_number,
                icon = "mobile",
                onSelect = function()
                    TriggerServerEvent("lb-phone:installSimOnPhone", phone.phone_number, simType)
                end
            })
        end

        -- Ouvrir le menu selon votre système
        if GetResourceState("ox_lib") == "started" then
            exports.ox_lib:registerContext({
                id = "phone_select_menu",
                title = "Choisir un téléphone",
                options = menuOptions
            })
            exports.ox_lib:showContext("phone_select_menu")
        elseif GetResourceState("qb-menu") == "started" then
            exports["qb-menu"]:openMenu(menuOptions)
        else
            print("Système de menu non supporté")
        end
    end)
end)

-- Intégration avec le système de téléphone existant
-- Ajouter un bouton dans l'interface du téléphone pour gérer les cartes SIM
RegisterNetEvent("lb-phone:openSimSettings", function(phoneNumber)
    SimSystemClient.OpenSimMenu(phoneNumber)
end)

-- Gestion des événements NUI
RegisterNUICallback("openSimSettings", function(data, cb)
    local phoneNumber = data.phoneNumber
    if phoneNumber then
        SimSystemClient.OpenSimMenu(phoneNumber)
    end
    cb("ok")
end)

-- Fonction pour mettre à jour l'affichage des informations SIM dans l'interface
function SimSystemClient.UpdateSimDisplay(phoneNumber)
    ESX.TriggerServerCallback("lb-phone:getInstalledSimCard", function(simInfo)
        SendNUIMessage({
            action = "updateSimInfo",
            simInfo = simInfo
        })
    end, phoneNumber)
end

-- Export pour utiliser depuis d'autres scripts
exports("UpdateSimDisplay", SimSystemClient.UpdateSimDisplay)

print("^2[LB Phone SIM Client] ^7Système de carte SIM client chargé avec succès")
