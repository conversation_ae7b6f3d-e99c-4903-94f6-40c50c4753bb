# to-regex [![NPM version](https://img.shields.io/npm/v/to-regex.svg?style=flat)](https://www.npmjs.com/package/to-regex) [![NPM monthly downloads](https://img.shields.io/npm/dm/to-regex.svg?style=flat)](https://npmjs.org/package/to-regex) [![NPM total downloads](https://img.shields.io/npm/dt/to-regex.svg?style=flat)](https://npmjs.org/package/to-regex) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/to-regex.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/to-regex)

> Generate a regex from a string or array of strings.

Please consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.

- [Install](#install)
- [Usage](#usage)
- [Options](#options)
  * [options.contains](#optionscontains)
  * [options.negate](#optionsnegate)
  * [options.nocase](#optionsnocase)
  * [options.flags](#optionsflags)
  * [options.cache](#optionscache)
  * [options.safe](#optionssafe)
- [About](#about)
  * [Related projects](#related-projects)
  * [Author](#author)
  * [License](#license)

_(TOC generated by [verb](https://github.com/verbose/verb) using [markdown-toc](https://github.com/jonschlinkert/markdown-toc))_

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save to-regex
```

## Usage

```js
var toRegex = require('to-regex');

console.log(toRegex('foo'));
//=> /^(?:foo)$/

console.log(toRegex('foo', {negate: true}));
//=> /^(?:(?:(?!^(?:foo)$).)*)$/

console.log(toRegex('foo', {contains: true}));
//=> /(?:foo)/

console.log(toRegex(['foo', 'bar'], {negate: true}));
//=> /^(?:(?:(?!^(?:(?:foo)|(?:bar))$).)*)$/

console.log(toRegex(['foo', 'bar'], {negate: true, contains: true}));
//=> /^(?:(?:(?!(?:(?:foo)|(?:bar))).)*)$/
```

## Options

### options.contains

**Type**: `Boolean`

**Default**: `undefined`

Generate a regex that will match any string that _contains_ the given pattern. By default, regex is strict will only return true for exact matches.

```js
var toRegex = require('to-regex');
console.log(toRegex('foo', {contains: true}));
//=> /(?:foo)/
```

### options.negate

**Type**: `Boolean`

**Default**: `undefined`

Create a regex that will match everything except the given pattern.

```js
var toRegex = require('to-regex');
console.log(toRegex('foo', {negate: true}));
//=> /^(?:(?:(?!^(?:foo)$).)*)$/
```

### options.nocase

**Type**: `Boolean`

**Default**: `undefined`

Adds the `i` flag, to enable case-insensitive matching.

```js
var toRegex = require('to-regex');
console.log(toRegex('foo', {nocase: true}));
//=> /^(?:foo)$/i
```

Alternatively you can pass the flags you want directly on [options.flags](#options.flags).

### options.flags

**Type**: `String`

**Default**: `undefined`

Define the flags you want to use on the generated regex.

```js
var toRegex = require('to-regex');
console.log(toRegex('foo', {flags: 'gm'}));
//=> /^(?:foo)$/gm
console.log(toRegex('foo', {flags: 'gmi', nocase: true})); //<= handles redundancy
//=> /^(?:foo)$/gmi
```

### options.cache

**Type**: `Boolean`

**Default**: `true`

Generated regex is cached based on the provided string and options. As a result, runtime compilation only happens once per pattern (as long as options are also the same), which can result in dramatic speed improvements.

This also helps with debugging, since adding options and pattern are added to the generated regex.

**Disable caching**

```js
toRegex('foo', {cache: false});
```

### options.safe

**Type**: `Boolean`

**Default**: `undefined`

Check the generated regular expression with [safe-regex](https://github.com/substack/safe-regex) and throw an error if the regex is potentially unsafe.

**Examples**

```js
console.log(toRegex('(x+x+)+y'));
//=> /^(?:(x+x+)+y)$/

// The following would throw an error
toRegex('(x+x+)+y', {safe: true});
```

## About

<details>
<summary><strong>Contributing</strong></summary>

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

</details>

<details>
<summary><strong>Running Tests</strong></summary>

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

</details>

<details>
<summary><strong>Building docs</strong></summary>

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

</details>

### Related projects

You might also be interested in these projects:

* [has-glob](https://www.npmjs.com/package/has-glob): Returns `true` if an array has a glob pattern. | [homepage](https://github.com/jonschlinkert/has-glob "Returns `true` if an array has a glob pattern.")
* [is-glob](https://www.npmjs.com/package/is-glob): Returns `true` if the given string looks like a glob pattern or an extglob pattern… [more](https://github.com/jonschlinkert/is-glob) | [homepage](https://github.com/jonschlinkert/is-glob "Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a bet")
* [path-regex](https://www.npmjs.com/package/path-regex): Regular expression for matching the parts of a file path. | [homepage](https://github.com/regexps/path-regex "Regular expression for matching the parts of a file path.")
* [to-regex-range](https://www.npmjs.com/package/to-regex-range): Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than… [more](https://github.com/micromatch/to-regex-range) | [homepage](https://github.com/micromatch/to-regex-range "Pass two numbers, get a regex-compatible source string for matching ranges. Validated against more than 2.78 million test assertions.")

### Author

**Jon Schlinkert**

* [linkedin/in/jonschlinkert](https://linkedin.com/in/jonschlinkert)
* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)

### License

Copyright © 2018, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on February 24, 2018._