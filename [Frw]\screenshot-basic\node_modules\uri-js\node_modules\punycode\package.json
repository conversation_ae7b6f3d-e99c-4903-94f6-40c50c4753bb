{"name": "punycode", "version": "2.1.1", "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "homepage": "https://mths.be/punycode", "main": "punycode.js", "jsnext:main": "punycode.es6.js", "module": "punycode.es6.js", "engines": {"node": ">=6"}, "keywords": ["punycode", "unicode", "idn", "idna", "dns", "url", "domain"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "contributors": [{"name": "<PERSON>", "url": "https://mathiasbynens.be/"}], "repository": {"type": "git", "url": "https://github.com/bestiejs/punycode.js.git"}, "bugs": "https://github.com/bestiejs/punycode.js/issues", "files": ["LICENSE-MIT.txt", "punycode.js", "punycode.es6.js"], "scripts": {"test": "mocha tests", "prepublish": "node scripts/prepublish.js"}, "devDependencies": {"codecov": "^1.0.1", "istanbul": "^0.4.1", "mocha": "^2.5.3"}, "jspm": {"map": {"./punycode.js": {"node": "@node/punycode"}}}}