{"name": "stream-http", "version": "2.8.3", "description": "Streaming http in the browser", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/jhiesey/stream-http.git"}, "scripts": {"test": "npm run test-node && ([ -n \"${TRAVIS_PULL_REQUEST}\" -a \"${TRAVIS_PULL_REQUEST}\" != 'false' ] || npm run test-browser)", "test-node": "tape test/node/*.js", "test-browser": "airtap --loopback airtap.local -- test/browser/*.js", "test-browser-local": "airtap --no-instrument --local 8080 -- test/browser/*.js"}, "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/jhiesey/stream-http/issues"}, "homepage": "https://github.com/jhiesey/stream-http#readme", "keywords": ["http", "stream", "streaming", "xhr", "http-browserify"], "dependencies": {"builtin-status-codes": "^3.0.0", "inherits": "^2.0.1", "readable-stream": "^2.3.6", "to-arraybuffer": "^1.0.0", "xtend": "^4.0.0"}, "devDependencies": {"airtap": "^0.0.5", "basic-auth": "^2.0.0", "brfs": "^1.6.1", "cookie-parser": "^1.4.3", "express": "^4.16.3", "tape": "^4.9.0", "ua-parser-js": "^0.7.18", "webworkify": "^1.5.0"}}