-- Configuration pour le système de carte SIM
-- Similaire au système d'accessoires d'armes mais pour les téléphones

Config.SimCard = {}

-- Configuration générale des cartes SIM
Config.SimCard.Enabled = true -- Activer/désactiver le système de carte SIM
Config.SimCard.RequireSimCard = false -- Exiger une carte SIM pour utiliser le téléphone
Config.SimCard.SlotName = "sim_slot" -- Nom du slot pour la carte SIM

-- Types de cartes SIM disponibles
Config.SimCard.Types = {
    {
        name = "sim_card_basic",
        label = "Carte SIM Basique",
        description = "Une carte SIM standard",
        weight = 0.1,
        price = 50,
        benefits = {
            call_quality = 1.0,
            data_speed = 1.0,
            signal_strength = 1.0
        }
    },
    {
        name = "sim_card_premium",
        label = "Carte SIM Premium",
        description = "Une carte SIM avec de meilleures performances",
        weight = 0.1,
        price = 150,
        benefits = {
            call_quality = 1.2,
            data_speed = 1.5,
            signal_strength = 1.3
        }
    },
    {
        name = "sim_card_unlimited",
        label = "Carte SIM Illimitée",
        description = "Une carte SIM avec données illimitées",
        weight = 0.1,
        price = 300,
        benefits = {
            call_quality = 1.5,
            data_speed = 2.0,
            signal_strength = 1.5,
            unlimited_data = true
        }
    }
}

-- Configuration des métadonnées pour les téléphones avec carte SIM
Config.SimCard.PhoneMetadata = {
    sim_slot = {
        name = nil,           -- Nom de la carte SIM installée
        type = nil,           -- Type de carte SIM
        phone_number = nil,   -- Numéro associé à la carte SIM
        benefits = {},        -- Avantages de la carte SIM
        installed_date = nil  -- Date d'installation
    }
}

-- Messages pour le système
Config.SimCard.Messages = {
    sim_installed = "Carte SIM installée avec succès",
    sim_removed = "Carte SIM retirée avec succès",
    sim_already_installed = "Une carte SIM est déjà installée",
    no_sim_installed = "Aucune carte SIM installée",
    invalid_sim_card = "Carte SIM invalide",
    phone_not_found = "Téléphone non trouvé",
    sim_required = "Une carte SIM est requise pour utiliser cette fonction"
}

-- Fonctions utilitaires pour les cartes SIM
function Config.SimCard.GetSimCardType(simName)
    for _, simType in ipairs(Config.SimCard.Types) do
        if simType.name == simName then
            return simType
        end
    end
    return nil
end

function Config.SimCard.IsValidSimCard(simName)
    return Config.SimCard.GetSimCardType(simName) ~= nil
end

function Config.SimCard.GetDefaultPhoneMetadata()
    return {
        sim_slot = {
            name = nil,
            type = nil,
            phone_number = nil,
            benefits = {},
            installed_date = nil
        }
    }
end
