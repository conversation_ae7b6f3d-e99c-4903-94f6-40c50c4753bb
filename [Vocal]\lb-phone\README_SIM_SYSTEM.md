# Système de Carte SIM pour LB Phone

Ce système ajoute la fonctionnalité de cartes SIM aux téléphones, similaire au système d'accessoires d'armes mais adapté pour les téléphones avec un seul slot.

## Fonctionnalités

- **Slot unique** : Chaque téléphone peut avoir une seule carte SIM installée
- **Types de cartes SIM** : Basique, Premium, et Illimitée avec différents avantages
- **Interface utilisateur** : Menu intégré dans les paramètres du téléphone
- **Système d'inventaire** : Compatible avec ox_inventory et qb-inventory
- **Achat en magasin** : Les cartes SIM sont disponibles dans les magasins

## Types de Cartes SIM

### Carte SIM Basique
- **Prix** : 50$
- **Avantages** : Performances standard
- **Qualité d'appel** : 100%
- **Vitesse de données** : 100%
- **Force du signal** : 100%

### Carte SIM Premium
- **Prix** : 150$
- **Avantages** : Meilleures performances
- **Qualité d'appel** : 120%
- **Vitesse de données** : 150%
- **Force du signal** : 130%

### Carte SIM Illimitée
- **Prix** : 300$
- **Avantages** : Performances maximales + données illimitées
- **Qualité d'appel** : 150%
- **Vitesse de données** : 200%
- **Force du signal** : 150%
- **Données illimitées** : Oui

## Installation

### 1. Fichiers ajoutés
- `config/sim_config.lua` - Configuration du système
- `server/custom/sim_system.lua` - Logique serveur
- `client/custom/sim_system.lua` - Logique client
- `ui/sim_integration.js` - Interface utilisateur

### 2. Modifications des fichiers existants
- `[Addons]\inventory\shared\items\items.lua` - Ajout des items cartes SIM
- `[Addons]\core\Config\Shop.lua` - Ajout des cartes SIM au magasin

### 3. Chargement des fichiers
Ajoutez ces lignes à votre `fxmanifest.lua` :

```lua
-- Configuration SIM
shared_script 'config/sim_config.lua'

-- Scripts SIM
server_script 'server/custom/sim_system.lua'
client_script 'client/custom/sim_system.lua'

-- Interface SIM
ui_page 'ui/sim_integration.js'
```

## Utilisation

### Pour les joueurs

#### Acheter une carte SIM
1. Allez dans un magasin
2. Achetez une carte SIM (Basique, Premium, ou Illimitée)

#### Installer une carte SIM
**Méthode 1 - Depuis l'inventaire :**
1. Utilisez la carte SIM depuis votre inventaire
2. Sélectionnez le téléphone sur lequel l'installer

**Méthode 2 - Depuis les paramètres du téléphone :**
1. Ouvrez votre téléphone
2. Allez dans les paramètres
3. Cliquez sur "Carte SIM"
4. Choisissez "Installer une carte SIM"
5. Sélectionnez la carte SIM à installer

#### Retirer une carte SIM
1. Ouvrez votre téléphone
2. Allez dans les paramètres
3. Cliquez sur "Carte SIM"
4. Cliquez sur "Retirer la carte SIM"

### Pour les développeurs

#### Exports disponibles

**Serveur :**
```lua
-- Installer une carte SIM
exports['lb-phone']:InstallSimCard(source, phoneNumber, simCardName)

-- Retirer une carte SIM
exports['lb-phone']:RemoveSimCard(source, phoneNumber)

-- Obtenir les informations de la carte SIM installée
exports['lb-phone']:GetInstalledSimCard(source, phoneNumber)
```

**Client :**
```lua
-- Ouvrir le menu de gestion SIM
exports['lb-phone']:OpenSimMenu(phoneNumber)

-- Mettre à jour l'affichage des informations SIM
exports['lb-phone']:UpdateSimDisplay(phoneNumber)
```

#### Events disponibles

**Serveur :**
```lua
-- Installer une carte SIM
TriggerServerEvent("lb-phone:installSimCard", phoneNumber, simCardName)

-- Retirer une carte SIM
TriggerServerEvent("lb-phone:removeSimCard", phoneNumber)
```

**Client :**
```lua
-- Résultat d'une opération SIM
RegisterNetEvent("lb-phone:simCardResult", function(success, message)
    -- success: boolean
    -- message: string
end)

-- Ouvrir les paramètres SIM
TriggerClientEvent("lb-phone:openSimSettings", source, phoneNumber)
```

## Configuration

Le fichier `config/sim_config.lua` permet de configurer :

- **Activation/désactivation** du système
- **Types de cartes SIM** et leurs avantages
- **Messages** d'interface
- **Métadonnées** par défaut

### Exemple de configuration personnalisée

```lua
-- Ajouter un nouveau type de carte SIM
table.insert(Config.SimCard.Types, {
    name = "sim_card_enterprise",
    label = "Carte SIM Entreprise",
    description = "Carte SIM pour usage professionnel",
    weight = 0.1,
    price = 500,
    benefits = {
        call_quality = 2.0,
        data_speed = 3.0,
        signal_strength = 2.0,
        unlimited_data = true,
        enterprise_features = true
    }
})
```

## Compatibilité

- **Frameworks** : ESX, QBCore, QBox
- **Inventaires** : ox_inventory, qb-inventory, autres inventaires compatibles
- **Menus** : ox_lib, qb-menu
- **Notifications** : ox_lib, qb-core, esx_notify

## Dépannage

### Problèmes courants

1. **Les cartes SIM n'apparaissent pas dans l'inventaire**
   - Vérifiez que les items sont bien ajoutés dans `items.lua`
   - Redémarrez le serveur

2. **Le menu SIM ne s'ouvre pas**
   - Vérifiez que votre système de menu est supporté
   - Consultez les logs pour les erreurs

3. **Les métadonnées ne se sauvegardent pas**
   - Vérifiez la compatibilité avec votre système d'inventaire
   - Assurez-vous que les téléphones sont configurés en mode unique

### Logs de débogage

Le système affiche des messages de débogage :
- `^2[LB Phone SIM] ^7Système de carte SIM chargé avec succès`
- `^2[LB Phone SIM Client] ^7Système de carte SIM client chargé avec succès`

## Support

Pour obtenir de l'aide ou signaler des bugs, consultez la documentation de LB Phone ou contactez le support technique.
