{"name": "ts-loader", "version": "5.4.5", "description": "TypeScript loader for webpack", "main": "index.js", "types": "dist/types/index.d.ts", "scripts": {"build": "tsc --version && tsc --project \"./src\"", "lint": "tslint --project \"./src\"", "comparison-tests": "tsc --project \"./test/comparison-tests\" && npm link ./test/comparison-tests/testLib && node test/comparison-tests/run-tests.js", "comparison-tests-generate": "node test/comparison-tests/stub-new-version.js", "execution-tests": "node test/execution-tests/run-tests.js", "test": "node test/run-tests.js", "docker:build": "docker build -t ts-loader .", "postdocker:build": "docker run -it ts-loader yarn test"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{ts,md}": ["prettier --write", "git add"]}, "repository": {"type": "git", "url": "https://github.com/TypeStrong/ts-loader.git"}, "keywords": ["ts-loader", "typescript-loader", "webpack", "loader", "typescript", "ts"], "engines": {"node": ">=6.11.5"}, "author": "<PERSON> <<EMAIL>> (https://blog.johnnyreilly.com)", "contributors": ["<PERSON> <<EMAIL>> (https://blog.johnnyreilly.com)", "<PERSON> <<EMAIL>> (http://www.jbrantly.com/)"], "license": "MIT", "bugs": {"url": "https://github.com/TypeStrong/ts-loader/issues"}, "homepage": "https://github.com/TypeStrong/ts-loader", "dependencies": {"chalk": "^2.3.0", "enhanced-resolve": "^4.0.0", "loader-utils": "^1.0.2", "micromatch": "^3.1.4", "semver": "^5.0.1"}, "devDependencies": {"@types/micromatch": "^3.1.0", "@types/node": "*", "@types/semver": "^5.4.0", "@types/webpack": "^4.4.29", "babel": "^6.0.0", "babel-core": "^6.0.0", "babel-loader": "^7.0.0", "babel-polyfill": "^6.16.0", "babel-preset-es2015": "^6.0.0", "babel-preset-es2016": "^6.16.0", "babel-preset-react": "^6.0.0", "escape-string-regexp": "^1.0.3", "fs-extra": "^7.0.0", "glob": "^7.1.1", "html-webpack-plugin": "^3.2.0", "husky": "^1.0.0", "jasmine-core": "^3.0.0", "karma": "^3.0.0", "karma-chrome-launcher": "^2.2.0", "karma-jasmine": "^2.0.0", "karma-mocha-reporter": "^2.0.0", "karma-sourcemap-loader": "^0.3.6", "karma-webpack": "^4.0.0-rc.5", "lint-staged": "^7.0.0", "mkdirp": "^0.5.1", "mocha": "^5.0.0", "prettier": "^1.11.1", "rimraf": "^2.6.2", "tslint": "^5.11.0", "tslint-config-prettier": "^1.15.0", "typescript": "^3.1.1", "webpack": "^4.5.0", "webpack-cli": "^3.1.1"}, "peerDependencies": {"typescript": "*"}}