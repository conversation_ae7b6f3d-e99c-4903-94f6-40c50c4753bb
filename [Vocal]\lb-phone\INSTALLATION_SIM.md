# Installation Rapide - Système de Carte SIM

## Résumé
Ce système ajoute un slot unique pour carte SIM dans les téléphones, similaire au système d'accessoires d'armes mais adapté pour les téléphones.

## Fichiers Créés/Modifiés

### ✅ Fichiers Créés
- `[Vocal]\lb-phone\config\sim_config.lua` - Configuration du système
- `[Vocal]\lb-phone\server\custom\sim_system.lua` - Logique serveur
- `[Vocal]\lb-phone\client\custom\sim_system.lua` - Logique client  
- `[Vocal]\lb-phone\ui\sim_integration.js` - Interface utilisateur
- `[Vocal]\lb-phone\test_sim_system.lua` - Tests du système
- `[Vocal]\lb-phone\README_SIM_SYSTEM.md` - Documentation complète

### ✅ Fichiers Modifiés
- `[Addons]\inventory\shared\items\items.lua` - Ajout des 3 types de cartes SIM
- `[Addons]\core\Config\Shop.lua` - Ajout des cartes SIM au magasin
- `[Vocal]\lb-phone\fxmanifest.lua` - Ajout du fichier JS

## Installation

### 1. Redémarrer le serveur
Les fichiers sont déjà en place, redémarrez simplement votre serveur pour charger le système.

### 2. Vérification
Utilisez ces commandes pour tester :
```
/testsimitems - Vérifier que les items sont chargés
/addsim sim_card_basic 1 - Ajouter une carte SIM basique
/testsim [numéro_téléphone] - Test complet du système
```

## Utilisation Joueur

### Acheter une carte SIM
1. Allez dans un magasin (24/7, etc.)
2. Achetez une carte SIM :
   - **Basique** : 50$ (performances standard)
   - **Premium** : 150$ (meilleures performances)
   - **Illimitée** : 300$ (performances max + données illimitées)

### Installer une carte SIM
**Méthode 1 - Depuis l'inventaire :**
1. Cliquez sur la carte SIM dans votre inventaire
2. Sélectionnez le téléphone

**Méthode 2 - Depuis le téléphone :**
1. Ouvrez votre téléphone
2. Paramètres → Carte SIM
3. "Installer une carte SIM"

### Retirer une carte SIM
1. Téléphone → Paramètres → Carte SIM
2. "Retirer la carte SIM"

## Fonctionnalités

### Types de Cartes SIM
| Type | Prix | Qualité Appel | Vitesse Données | Signal | Spécial |
|------|------|---------------|-----------------|--------|---------|
| Basique | 50$ | 100% | 100% | 100% | - |
| Premium | 150$ | 120% | 150% | 130% | - |
| Illimitée | 300$ | 150% | 200% | 150% | Données illimitées |

### Système de Slot
- **1 slot unique** par téléphone
- **Métadonnées sauvegardées** dans l'inventaire
- **Compatible** avec ox_inventory et qb-inventory
- **Interface intégrée** dans les paramètres du téléphone

## Exports Disponibles

### Serveur
```lua
-- Installer une carte SIM
exports['lb-phone']:InstallSimCard(source, phoneNumber, simCardName)

-- Retirer une carte SIM  
exports['lb-phone']:RemoveSimCard(source, phoneNumber)

-- Obtenir les infos SIM
exports['lb-phone']:GetInstalledSimCard(source, phoneNumber)
```

### Client
```lua
-- Ouvrir le menu SIM
exports['lb-phone']:OpenSimMenu(phoneNumber)

-- Mettre à jour l'affichage
exports['lb-phone']:UpdateSimDisplay(phoneNumber)
```

## Dépannage

### Problème : Les cartes SIM n'apparaissent pas
**Solution :** Redémarrez le serveur pour charger les nouveaux items

### Problème : Le menu ne s'ouvre pas
**Solution :** Vérifiez que ox_lib ou qb-menu est installé

### Problème : Les métadonnées ne se sauvegardent pas
**Solution :** Assurez-vous que Config.Item.Unique = true dans lb-phone

## Test Rapide

1. Connectez-vous au serveur
2. Exécutez `/addsim sim_card_basic 1`
3. Ouvrez votre inventaire et utilisez la carte SIM
4. Sélectionnez votre téléphone
5. Vérifiez dans Paramètres → Carte SIM

## Configuration

Modifiez `config/sim_config.lua` pour :
- Activer/désactiver le système
- Ajouter de nouveaux types de cartes SIM
- Modifier les avantages des cartes
- Personnaliser les messages

## Support

- Consultez `README_SIM_SYSTEM.md` pour la documentation complète
- Utilisez `test_sim_system.lua` pour diagnostiquer les problèmes
- Vérifiez les logs serveur pour les erreurs

---

**✅ Installation terminée !** Le système est maintenant opérationnel et similaire au système d'accessoires d'armes mais adapté pour un slot unique de carte SIM dans les téléphones.
