{"name": "typescript", "author": "Microsoft Corp.", "homepage": "https://www.typescriptlang.org/", "version": "3.2.2", "license": "Apache-2.0", "description": "TypeScript is a language for application scale JavaScript development", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript"], "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "repository": {"type": "git", "url": "https://github.com/Microsoft/TypeScript.git"}, "main": "./lib/typescript.js", "typings": "./lib/typescript.d.ts", "bin": {"tsc": "./bin/tsc", "tsserver": "./bin/tsserver"}, "engines": {"node": ">=4.2.0"}, "devDependencies": {"@octokit/rest": "latest", "@types/browserify": "latest", "@types/chai": "latest", "@types/convert-source-map": "latest", "@types/del": "latest", "@types/glob": "latest", "@types/gulp": "3.X", "@types/gulp-concat": "latest", "@types/gulp-help": "latest", "@types/gulp-if": "0.0.33", "@types/gulp-newer": "latest", "@types/gulp-rename": "0.0.33", "@types/gulp-sourcemaps": "0.0.32", "@types/jake": "latest", "@types/merge2": "latest", "@types/minimatch": "latest", "@types/minimist": "latest", "@types/mkdirp": "latest", "@types/mocha": "latest", "@types/node": "8.5.5", "@types/q": "latest", "@types/run-sequence": "latest", "@types/source-map-support": "latest", "@types/through2": "latest", "@types/travis-fold": "latest", "@types/xml2js": "^0.4.0", "browser-resolve": "^1.11.2", "browserify": "latest", "chai": "latest", "chalk": "latest", "convert-source-map": "latest", "del": "latest", "fancy-log": "latest", "fs-extra": "^6.0.1", "gulp": "3.X", "gulp-clone": "latest", "gulp-concat": "latest", "gulp-help": "latest", "gulp-if": "latest", "gulp-insert": "latest", "gulp-newer": "latest", "gulp-rename": "latest", "gulp-sourcemaps": "latest", "gulp-typescript": "latest", "istanbul": "latest", "jake": "latest", "lodash": "4.17.10", "merge2": "latest", "minimist": "latest", "mkdirp": "latest", "mocha": "latest", "mocha-fivemat-progress-reporter": "latest", "plugin-error": "latest", "prex": "^0.4.3", "q": "latest", "remove-internal": "^2.9.2", "run-sequence": "latest", "source-map-support": "latest", "through2": "latest", "travis-fold": "latest", "tslint": "latest", "typescript": "next", "vinyl": "latest", "vinyl-sourcemaps-apply": "latest", "xml2js": "^0.4.19"}, "scripts": {"pretest": "jake tests", "test": "jake runtests-parallel light=false", "build": "npm run build:compiler && npm run build:tests", "build:compiler": "jake local", "build:tests": "jake tests", "start": "node lib/tsc", "clean": "jake clean", "gulp": "gulp", "jake": "jake", "lint": "jake lint", "setup-hooks": "node scripts/link-hooks.js"}, "browser": {"fs": false, "os": false, "path": false}, "dependencies": {}}