fx_version "cerulean"
game "gta5"
lua54 "yes"

version "2.3.4"

shared_script {
    "config/*.lua",
    "shared/**/*.lua"
}

client_script {
    "lib/client/**.lua",
    "client/**.lua"
}

server_scripts {
    "@mysql-async/lib/MySQL.lua",
    "lib/server/**.lua",
    "server/**/*.lua",
}

files {
    "ui/dist/**/*",
    "ui/components.js",
    "ui/sim_integration.js",
    "config/**/*"
}

ui_page "ui/dist/index.html"

dependency "mysql-async"

escrow_ignore {
    "config/**/*",

    "client/apps/framework/**/*.lua",
    "server/apps/framework/**/*.lua",
    "shared/*.lua",

    "client/custom/**/*.lua",
    "server/custom/**/*.lua",

    "client/misc/debug.lua",
    "server/misc/debug.lua",

    "server/misc/functions.lua",
    "server/misc/databaseChecker/*.lua",

    "server/apiKeys.lua",

    "types.lua",

    "client/apps/default/weather.lua",

    "lib/**/*",
}

dependency '/assetpacks'